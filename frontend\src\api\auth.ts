import api from './index'

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  token: string
  username: string
  email: string
}

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

/**
 * 用户登录
 */
export const login = (data: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
  return api.post('/auth/login', data)
}

/**
 * 验证Token
 */
export const validateToken = (token: string): Promise<ApiResponse<string>> => {
  return api.get('/auth/validate', { params: { token } })
}

/**
 * 用户登出
 */
export const logout = (): Promise<ApiResponse<string>> => {
  return api.post('/auth/logout')
}
