package com.findme.repository;

import com.findme.entity.ContactInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 联系方式信息数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface ContactInfoRepository extends JpaRepository<ContactInfo, Long> {

    /**
     * 查找所有启用的联系方式，按显示顺序排序
     * 
     * @return 联系方式列表
     */
    List<ContactInfo> findByIsEnabledTrueOrderByDisplayOrderAsc();

    /**
     * 查找所有联系方式，按显示顺序排序
     * 
     * @return 联系方式列表
     */
    List<ContactInfo> findAllByOrderByDisplayOrderAsc();

    /**
     * 根据平台名称查找联系方式
     * 
     * @param platform 平台名称
     * @return 联系方式列表
     */
    List<ContactInfo> findByPlatform(String platform);

    /**
     * 检查平台是否已存在
     * 
     * @param platform 平台名称
     * @return 是否存在
     */
    boolean existsByPlatform(String platform);

    /**
     * 获取最大显示顺序
     * 
     * @return 最大显示顺序
     */
    @Query("SELECT COALESCE(MAX(c.displayOrder), 0) FROM ContactInfo c")
    Integer findMaxDisplayOrder();
}
