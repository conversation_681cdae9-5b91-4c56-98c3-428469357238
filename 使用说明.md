# FindMe 使用说明

## 🎯 项目概述

FindMe 是一个炫酷的个人防失联网站，专为展示个人联系方式而设计。网站采用现代化的技术栈，提供了美观的前台展示和便捷的后台管理功能。

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

1. **双击运行 `start.bat`**
   - 脚本会自动检查环境
   - 启动后端服务
   - 安装前端依赖并启动前端服务

### 方法二：手动启动

#### 1. 准备数据库
```sql
-- 创建数据库
CREATE DATABASE findme CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入初始化脚本
mysql -u root -p findme < database/init.sql
```

#### 2. 启动后端
```bash
cd backend
mvn spring-boot:run
```

#### 3. 启动前端
```bash
cd frontend
npm install
npm run dev
```

## 🔧 配置修改

### 数据库配置
编辑 `backend/src/main/resources/application.yml`：
```yaml
spring:
  datasource:
    url: **********************************
    username: 你的数据库用户名
    password: 你的数据库密码
```

### JWT密钥配置
```yaml
jwt:
  secret: 你的JWT密钥
  expiration: 86400000  # 24小时
```

## 📱 功能使用

### 前台功能

1. **主页展示**
   - 访问 `http://localhost:3000`
   - 查看炫酷的主页设计
   - 点击联系方式卡片复制信息

2. **响应式设计**
   - 支持桌面端和移动端
   - 自适应不同屏幕尺寸

### 后台管理

1. **登录管理后台**
   - 访问 `http://localhost:3000/admin`
   - 默认账户：`admin`
   - 默认密码：`admin123`

2. **仪表盘**
   - 查看统计信息
   - 快速操作入口
   - 最近更新记录

3. **联系方式管理**
   - 添加新的联系方式
   - 编辑现有联系方式
   - 启用/禁用联系方式
   - 删除不需要的联系方式
   - 调整显示顺序

## 🎨 个性化定制

### 修改主标题
编辑 `frontend/src/views/Home.vue`：
```vue
<h1 class="main-title neon-text">你的个性化标题</h1>
```

### 修改副标题
```vue
<p class="subtitle" ref="subtitleRef">你的副标题</p>
```

### 添加新的联系方式平台
1. 在后台管理中添加新的联系方式
2. 设置平台名称、联系方式值、图标URL等
3. 调整显示顺序

### 修改颜色主题
编辑 `frontend/src/styles/global.scss`：
```scss
// 修改渐变背景
.gradient-bg {
  background: linear-gradient(135deg, #你的颜色1 0%, #你的颜色2 100%);
}
```

## 🔒 安全设置

### 修改默认管理员密码
1. 登录管理后台
2. 或直接在数据库中修改 `admin_user` 表
3. 密码使用 BCrypt 加密

### JWT密钥安全
- 生产环境请使用强密钥
- 定期更换JWT密钥
- 设置合适的过期时间

## 📦 部署指南

### 后端部署
```bash
# 打包
cd backend
mvn clean package

# 运行
java -jar target/findme-backend-1.0.0.jar
```

### 前端部署
```bash
# 构建
cd frontend
npm run build

# 将 dist 目录部署到 Web 服务器
```

### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理
    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🐛 常见问题

### 1. 后端启动失败
- 检查Java版本（需要17+）
- 检查数据库连接配置
- 确保MySQL服务已启动
- 检查端口8080是否被占用

### 2. 前端启动失败
- 检查Node.js版本（需要16+）
- 删除 `node_modules` 重新安装
- 检查端口3000是否被占用

### 3. 数据库连接失败
- 确认数据库服务已启动
- 检查用户名密码是否正确
- 确认数据库 `findme` 已创建
- 检查防火墙设置

### 4. 登录失败
- 确认使用默认账户 `admin/admin123`
- 检查数据库中是否有用户数据
- 查看后端日志排查问题

### 5. 前台无法显示联系方式
- 检查后端API是否正常
- 确认数据库中有联系方式数据
- 检查网络请求是否成功

## 📞 技术支持

如遇到问题，请按以下步骤排查：

1. **查看控制台日志**
   - 后端日志：查看启动窗口
   - 前端日志：浏览器开发者工具

2. **检查网络请求**
   - 打开浏览器开发者工具
   - 查看Network标签页
   - 确认API请求状态

3. **数据库检查**
   - 确认数据库连接正常
   - 检查表结构和数据

4. **环境检查**
   - Java版本：`java -version`
   - Node.js版本：`node --version`
   - Maven版本：`mvn --version`

## 🎉 享受使用

现在您可以：
- 🌟 拥有一个炫酷的个人展示网站
- 📱 在任何设备上完美展示
- 🔧 轻松管理您的联系方式
- 🚀 随时更新和维护内容

祝您使用愉快！
