<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表盘</h1>
      <p>欢迎回来，{{ authStore.username }}！</p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Phone /></el-icon>
        </div>
        <div class="stat-content">
          <h3>{{ totalContacts }}</h3>
          <p>总联系方式</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon enabled">
          <el-icon><Check /></el-icon>
        </div>
        <div class="stat-content">
          <h3>{{ enabledContacts }}</h3>
          <p>已启用</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon disabled">
          <el-icon><Close /></el-icon>
        </div>
        <div class="stat-content">
          <h3>{{ disabledContacts }}</h3>
          <p>已禁用</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><View /></el-icon>
        </div>
        <div class="stat-content">
          <h3>{{ todayViews }}</h3>
          <p>今日访问</p>
        </div>
      </div>
    </div>
    
    <!-- 快速操作 -->
    <div class="quick-actions">
      <h2>快速操作</h2>
      <div class="actions-grid">
        <el-card class="action-card" @click="goToContacts">
          <div class="action-content">
            <el-icon><Phone /></el-icon>
            <h3>管理联系方式</h3>
            <p>添加、编辑或删除联系方式</p>
          </div>
        </el-card>
        
        <el-card class="action-card" @click="goToHome">
          <div class="action-content">
            <el-icon><House /></el-icon>
            <h3>查看前台</h3>
            <p>预览前台展示效果</p>
          </div>
        </el-card>
        
        <el-card class="action-card" @click="refreshData">
          <div class="action-content">
            <el-icon><Refresh /></el-icon>
            <h3>刷新数据</h3>
            <p>重新加载最新数据</p>
          </div>
        </el-card>
      </div>
    </div>
    
    <!-- 最近更新 -->
    <div class="recent-updates">
      <h2>最近更新</h2>
      <el-card>
        <div class="updates-list">
          <div 
            v-for="contact in recentContacts" 
            :key="contact.id"
            class="update-item"
          >
            <div class="update-icon">
              <img :src="contact.iconUrl" :alt="contact.platform" />
            </div>
            <div class="update-content">
              <h4>{{ contact.platform }}</h4>
              <p>{{ contact.contactValue }}</p>
              <span class="update-time">
                {{ formatTime(contact.updateTime) }}
              </span>
            </div>
            <div class="update-status">
              <el-tag :type="contact.isEnabled ? 'success' : 'danger'">
                {{ contact.isEnabled ? '已启用' : '已禁用' }}
              </el-tag>
            </div>
          </div>
          
          <div v-if="recentContacts.length === 0" class="no-data">
            <el-empty description="暂无数据" />
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useContactsStore } from '@/stores/contacts'
import { ElMessage } from 'element-plus'
import {
  Phone,
  Check,
  Close,
  View,
  House,
  Refresh
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const contactsStore = useContactsStore()

// 响应式数据
const todayViews = ref(Math.floor(Math.random() * 100) + 50) // 模拟数据

// 计算属性
const totalContacts = computed(() => contactsStore.adminContacts.length)

const enabledContacts = computed(() => 
  contactsStore.adminContacts.filter(c => c.isEnabled).length
)

const disabledContacts = computed(() => 
  contactsStore.adminContacts.filter(c => !c.isEnabled).length
)

const recentContacts = computed(() => 
  [...contactsStore.adminContacts]
    .sort((a, b) => new Date(b.updateTime || '').getTime() - new Date(a.updateTime || '').getTime())
    .slice(0, 5)
)

// 格式化时间
const formatTime = (timeStr?: string) => {
  if (!timeStr) return ''
  
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return time.toLocaleDateString()
}

// 跳转到联系方式管理
const goToContacts = () => {
  router.push('/admin/contacts')
}

// 跳转到首页
const goToHome = () => {
  window.open('/', '_blank')
}

// 刷新数据
const refreshData = async () => {
  try {
    await contactsStore.fetchAdminContacts()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

// 组件挂载
onMounted(async () => {
  await contactsStore.fetchAdminContacts()
})
</script>

<style lang="scss" scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 30px;
  
  h1 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 8px;
  }
  
  p {
    color: #666;
    font-size: 1rem;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
  }
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #409eff;
  color: #fff;
  font-size: 24px;
  
  &.enabled {
    background: #67c23a;
  }
  
  &.disabled {
    background: #f56c6c;
  }
}

.stat-content {
  h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
  }
  
  p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
  }
}

.quick-actions {
  margin-bottom: 40px;
  
  h2 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 20px;
  }
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
  
  :deep(.el-card__body) {
    padding: 24px;
  }
}

.action-content {
  text-align: center;
  
  .el-icon {
    font-size: 48px;
    color: #409eff;
    margin-bottom: 16px;
  }
  
  h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 8px;
  }
  
  p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
  }
}

.recent-updates {
  h2 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 20px;
  }
}

.updates-list {
  .update-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .update-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .update-content {
    flex: 1;
    
    h4 {
      font-size: 1rem;
      color: #333;
      margin-bottom: 4px;
    }
    
    p {
      color: #666;
      font-size: 0.9rem;
      margin-bottom: 4px;
    }
    
    .update-time {
      color: #999;
      font-size: 0.8rem;
    }
  }
  
  .update-status {
    flex-shrink: 0;
  }
  
  .no-data {
    padding: 40px 0;
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-header h1 {
    font-size: 1.5rem;
  }
}
</style>
