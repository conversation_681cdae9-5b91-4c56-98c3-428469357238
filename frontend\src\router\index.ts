import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/Home.vue'),
      meta: {
        title: 'FindMe - 专注网易一手'
      }
    },
    {
      path: '/admin',
      name: 'Admin',
      component: () => import('@/views/admin/Layout.vue'),
      meta: {
        requiresAuth: true,
        title: '管理后台'
      },
      children: [
        {
          path: '',
          name: 'AdminDashboard',
          component: () => import('@/views/admin/Dashboard.vue'),
          meta: {
            title: '仪表盘'
          }
        },
        {
          path: 'contacts',
          name: 'AdminContacts',
          component: () => import('@/views/admin/Contacts.vue'),
          meta: {
            title: '联系方式管理'
          }
        }
      ]
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: {
        title: '管理员登录'
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue'),
      meta: {
        title: '页面未找到'
      }
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title as string
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // 尝试从本地存储恢复登录状态
      await authStore.checkAuth()
      
      if (!authStore.isAuthenticated) {
        next('/login')
        return
      }
    }
  }
  
  // 如果已登录用户访问登录页，重定向到管理后台
  if (to.name === 'Login' && authStore.isAuthenticated) {
    next('/admin')
    return
  }
  
  next()
})

export default router
