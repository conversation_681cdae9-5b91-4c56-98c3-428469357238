package com.findme.controller;

import com.findme.dto.ApiResponse;
import com.findme.dto.ContactInfoDTO;
import com.findme.entity.ContactInfo;
import com.findme.service.ContactInfoService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 联系方式控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/contacts")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class ContactController {

    private final ContactInfoService contactInfoService;

    /**
     * 获取所有启用的联系方式（前台展示）
     * 
     * @return 联系方式列表
     */
    @GetMapping("/public")
    public ApiResponse<List<ContactInfo>> getPublicContacts() {
        try {
            List<ContactInfo> contacts = contactInfoService.getEnabledContacts();
            return ApiResponse.success(contacts);
        } catch (Exception e) {
            log.error("获取公开联系方式失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取所有联系方式（后台管理）
     * 
     * @return 联系方式列表
     */
    @GetMapping("/admin")
    public ApiResponse<List<ContactInfo>> getAllContacts() {
        try {
            List<ContactInfo> contacts = contactInfoService.getAllContacts();
            return ApiResponse.success(contacts);
        } catch (Exception e) {
            log.error("获取所有联系方式失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 根据ID获取联系方式
     * 
     * @param id 联系方式ID
     * @return 联系方式信息
     */
    @GetMapping("/{id}")
    public ApiResponse<ContactInfo> getContactById(@PathVariable Long id) {
        try {
            ContactInfo contact = contactInfoService.getContactById(id);
            return ApiResponse.success(contact);
        } catch (Exception e) {
            log.error("获取联系方式失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 创建联系方式
     * 
     * @param contactInfoDTO 联系方式DTO
     * @return 创建的联系方式
     */
    @PostMapping
    public ApiResponse<ContactInfo> createContact(@Valid @RequestBody ContactInfoDTO contactInfoDTO) {
        try {
            ContactInfo contact = contactInfoService.createContact(contactInfoDTO);
            return ApiResponse.success("联系方式创建成功", contact);
        } catch (Exception e) {
            log.error("创建联系方式失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 更新联系方式
     * 
     * @param id 联系方式ID
     * @param contactInfoDTO 联系方式DTO
     * @return 更新的联系方式
     */
    @PutMapping("/{id}")
    public ApiResponse<ContactInfo> updateContact(@PathVariable Long id, 
                                                  @Valid @RequestBody ContactInfoDTO contactInfoDTO) {
        try {
            ContactInfo contact = contactInfoService.updateContact(id, contactInfoDTO);
            return ApiResponse.success("联系方式更新成功", contact);
        } catch (Exception e) {
            log.error("更新联系方式失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 删除联系方式
     * 
     * @param id 联系方式ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteContact(@PathVariable Long id) {
        try {
            contactInfoService.deleteContact(id);
            return ApiResponse.success("联系方式删除成功");
        } catch (Exception e) {
            log.error("删除联系方式失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 切换联系方式启用状态
     * 
     * @param id 联系方式ID
     * @return 更新后的联系方式
     */
    @PatchMapping("/{id}/toggle")
    public ApiResponse<ContactInfo> toggleContactStatus(@PathVariable Long id) {
        try {
            ContactInfo contact = contactInfoService.toggleContactStatus(id);
            return ApiResponse.success("状态切换成功", contact);
        } catch (Exception e) {
            log.error("切换联系方式状态失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        }
    }
}
