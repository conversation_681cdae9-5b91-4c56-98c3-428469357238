package com.findme.config;

import com.findme.service.AuthService;
import com.findme.service.ContactInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 数据初始化器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {

    private final AuthService authService;
    private final ContactInfoService contactInfoService;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化数据...");
        
        // 创建默认管理员用户
        authService.createDefaultAdmin();
        
        // 初始化默认联系方式
        contactInfoService.initDefaultContacts();
        
        log.info("数据初始化完成!");
    }
}
