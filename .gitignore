# 编译输出
target/
dist/
build/

# 依赖目录
node_modules/
.pnp
.pnp.js

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov
.nyc_output

# 依赖锁定文件 (可选择性忽略)
# package-lock.json
# yarn.lock

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# OS 生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Java 相关
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*

# Maven
.mvn/
mvnw
mvnw.cmd

# Gradle
.gradle/
gradle/
gradlew
gradlew.bat

# Spring Boot
application-*.yml
application-*.yaml
application-*.properties

# 数据库
*.db
*.sqlite
*.sqlite3

# 临时文件
*.tmp
*.temp
*.bak
*.swp
*.swo

# 缓存
.cache/
.parcel-cache/

# 测试
.jest/
coverage/

# 构建工具
.vite/
.rollup.cache/

# TypeScript
*.tsbuildinfo

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 可选的 REPL 历史
.node_repl_history

# 输出的 npm 包
*.tgz

# Yarn Integrity 文件
.yarn-integrity

# dotenv 环境变量文件
.env.test

# parcel-bundler 缓存 (https://parceljs.org/)
.cache
.parcel-cache

# Next.js 构建输出
.next

# Nuxt.js 构建 / 生成输出
.nuxt
dist

# Gatsby 文件
.cache/
public

# Storybook 构建输出
.out
.storybook-out

# 临时文件夹
tmp/
temp/

# 自动生成的文件
auto-imports.d.ts
components.d.ts
