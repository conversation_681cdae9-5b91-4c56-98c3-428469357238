@echo off
chcp 65001 >nul
echo ========================================
echo    FindMe 个人防失联网站启动脚本
echo ========================================
echo.

echo [1] 检查环境...
echo.

:: 检查Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java 未安装或未配置环境变量
    echo 请安装 Java 17 或更高版本
    pause
    exit /b 1
) else (
    echo ✅ Java 环境检查通过
)

:: 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装或未配置环境变量
    echo 请安装 Node.js 16 或更高版本
    pause
    exit /b 1
) else (
    echo ✅ Node.js 环境检查通过
)

:: 检查Maven
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven 未安装或未配置环境变量
    echo 请安装 Maven 3.6 或更高版本
    pause
    exit /b 1
) else (
    echo ✅ Maven 环境检查通过
)

echo.
echo [2] 启动后端服务...
echo.

:: 启动后端
start "FindMe Backend" cmd /k "cd /d backend && echo 正在启动后端服务... && mvn spring-boot:run"

:: 等待后端启动
echo 等待后端服务启动中...
timeout /t 10 /nobreak >nul

echo.
echo [3] 安装前端依赖并启动...
echo.

:: 检查前端依赖
if not exist "frontend\node_modules" (
    echo 正在安装前端依赖...
    cd frontend
    npm install
    cd ..
)

:: 启动前端
start "FindMe Frontend" cmd /k "cd /d frontend && echo 正在启动前端服务... && npm run dev"

echo.
echo ========================================
echo 🚀 FindMe 启动完成！
echo ========================================
echo.
echo 📱 前台地址: http://localhost:3000
echo 🔧 后台地址: http://localhost:3000/admin
echo 🔑 默认账户: admin / admin123
echo 📡 API地址:  http://localhost:8080/api
echo.
echo 💡 提示：
echo - 请确保MySQL数据库已启动并导入初始化脚本
echo - 如需修改配置，请编辑相应的配置文件
echo - 按任意键退出此窗口
echo.
pause
