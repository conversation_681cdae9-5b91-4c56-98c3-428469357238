import { defineStore } from 'pinia'
import { ref } from 'vue'
import { 
  getPublicContacts, 
  getAllContacts, 
  createContact, 
  updateContact, 
  deleteContact, 
  toggleContactStatus,
  type ContactInfo,
  type ContactInfoDTO 
} from '@/api/contacts'
import { ElMessage } from 'element-plus'

export const useContactsStore = defineStore('contacts', () => {
  // 状态
  const publicContacts = ref<ContactInfo[]>([])
  const adminContacts = ref<ContactInfo[]>([])
  const loading = ref<boolean>(false)

  // 获取公开联系方式
  const fetchPublicContacts = async () => {
    try {
      loading.value = true
      const response = await getPublicContacts()
      publicContacts.value = response.data || []
    } catch (error) {
      console.error('获取公开联系方式失败:', error)
      ElMessage.error('获取联系方式失败')
    } finally {
      loading.value = false
    }
  }

  // 获取所有联系方式（管理后台）
  const fetchAdminContacts = async () => {
    try {
      loading.value = true
      const response = await getAllContacts()
      adminContacts.value = response.data || []
    } catch (error) {
      console.error('获取管理联系方式失败:', error)
      ElMessage.error('获取联系方式失败')
    } finally {
      loading.value = false
    }
  }

  // 创建联系方式
  const addContact = async (contactData: ContactInfoDTO) => {
    try {
      loading.value = true
      const response = await createContact(contactData)
      
      if (response.data) {
        adminContacts.value.push(response.data)
        ElMessage.success('联系方式创建成功')
        return true
      }
      return false
    } catch (error) {
      console.error('创建联系方式失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 更新联系方式
  const editContact = async (id: number, contactData: ContactInfoDTO) => {
    try {
      loading.value = true
      const response = await updateContact(id, contactData)
      
      if (response.data) {
        const index = adminContacts.value.findIndex(c => c.id === id)
        if (index !== -1) {
          adminContacts.value[index] = response.data
        }
        ElMessage.success('联系方式更新成功')
        return true
      }
      return false
    } catch (error) {
      console.error('更新联系方式失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 删除联系方式
  const removeContact = async (id: number) => {
    try {
      loading.value = true
      await deleteContact(id)
      
      adminContacts.value = adminContacts.value.filter(c => c.id !== id)
      ElMessage.success('联系方式删除成功')
      return true
    } catch (error) {
      console.error('删除联系方式失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 切换联系方式状态
  const toggleStatus = async (id: number) => {
    try {
      const response = await toggleContactStatus(id)
      
      if (response.data) {
        const index = adminContacts.value.findIndex(c => c.id === id)
        if (index !== -1) {
          adminContacts.value[index] = response.data
        }
        ElMessage.success('状态切换成功')
        return true
      }
      return false
    } catch (error) {
      console.error('切换状态失败:', error)
      return false
    }
  }

  return {
    // 状态
    publicContacts,
    adminContacts,
    loading,
    
    // 方法
    fetchPublicContacts,
    fetchAdminContacts,
    addContact,
    editContact,
    removeContact,
    toggleStatus
  }
})
