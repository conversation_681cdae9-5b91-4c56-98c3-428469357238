package com.findme.controller;

import com.findme.dto.ApiResponse;
import com.findme.dto.LoginRequest;
import com.findme.service.AuthService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class AuthController {

    private final AuthService authService;

    /**
     * 用户登录
     * 
     * @param loginRequest 登录请求
     * @return 登录结果
     */
    @PostMapping("/login")
    public ApiResponse<Map<String, Object>> login(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            Map<String, Object> result = authService.login(loginRequest);
            return ApiResponse.success("登录成功", result);
        } catch (Exception e) {
            log.error("登录失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 验证Token
     * 
     * @param token JWT Token
     * @return 验证结果
     */
    @GetMapping("/validate")
    public ApiResponse<String> validateToken(@RequestParam String token) {
        try {
            authService.validateToken(token);
            return ApiResponse.success("Token有效");
        } catch (Exception e) {
            log.error("Token验证失败: {}", e.getMessage());
            return ApiResponse.unauthorized(e.getMessage());
        }
    }

    /**
     * 用户登出
     * 
     * @return 登出结果
     */
    @PostMapping("/logout")
    public ApiResponse<String> logout() {
        // JWT是无状态的，客户端删除token即可
        return ApiResponse.success("登出成功");
    }
}
