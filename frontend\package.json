{"name": "findme-frontend", "version": "1.0.0", "description": "FindMe个人防失联网站前端", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.1.0", "animate.css": "^4.1.1", "particles.js": "^2.0.0", "typed.js": "^2.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/tsconfig": "^0.4.0", "typescript": "~5.2.0", "vue-tsc": "^1.8.22", "vite": "^5.0.0", "@types/node": "^20.9.0", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "sass": "^1.69.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}