# FindMe - 个人防失联网站需求文档

## 项目概述
一个炫酷的个人博客/防失联网站，主要用于展示个人联系方式，确保用户能够通过多种渠道联系到您。

## 技术栈
- **后端**: Java (Spring Boot)
- **前端**: Vue 3 + TypeScript
- **数据库**: MySQL
- **UI框架**: Element Plus / Ant Design Vue
- **样式**: CSS3 动画 + 炫酷特效

## 功能需求

### 1. 前台展示页面
#### 1.1 主页设计
- **大标题**: "专注网易一手" - 使用炫酷字体和动画效果
- **副标题**: "找到我见下" - 简洁明了的引导文字
- **联系方式展示区域**:
  - 钉钉
  - 飞书
  - 微信
  - QQ
- **视觉效果**:
  - 渐变背景或动态背景
  - 卡片式布局展示联系方式
  - 悬停动画效果
  - 响应式设计，支持移动端

#### 1.2 联系方式卡片
- 每个联系方式一个卡片
- 显示平台图标
- 显示联系方式（QQ号、微信号等）
- 点击可复制或跳转
- 悬停时有炫酷动画效果

### 2. 后台管理系统
#### 2.1 登录功能
- 简单的用户名密码登录
- JWT token认证
- 登录状态保持

#### 2.2 联系方式管理
- 查看所有联系方式
- 添加新的联系方式
- 编辑现有联系方式
- 删除联系方式
- 启用/禁用联系方式

#### 2.3 管理界面
- 简洁的后台管理界面
- 表格形式展示数据
- 表单编辑功能
- 操作确认提示

### 3. 数据库设计
#### 3.1 用户表 (admin_user)
```sql
- id: 主键
- username: 用户名
- password: 密码(加密)
- create_time: 创建时间
- update_time: 更新时间
```

#### 3.2 联系方式表 (contact_info)
```sql
- id: 主键
- platform: 平台名称(钉钉/微信/QQ/飞书)
- contact_value: 联系方式值
- icon_url: 图标URL
- display_order: 显示顺序
- is_enabled: 是否启用
- create_time: 创建时间
- update_time: 更新时间
```

## 非功能需求

### 1. 性能要求
- 页面加载时间 < 2秒
- 支持并发访问
- 移动端适配

### 2. 安全要求
- 后台管理需要身份验证
- 密码加密存储
- 防止SQL注入
- XSS防护

### 3. 用户体验
- 炫酷的视觉效果
- 流畅的动画过渡
- 直观的操作界面
- 响应式设计

## 项目结构
```
FindMe/
├── backend/                 # 后端项目
│   ├── src/main/java/
│   │   └── com/findme/
│   │       ├── controller/  # 控制器
│   │       ├── service/     # 服务层
│   │       ├── entity/      # 实体类
│   │       ├── repository/  # 数据访问层
│   │       └── config/      # 配置类
│   └── pom.xml
├── frontend/                # 前端项目
│   ├── src/
│   │   ├── components/      # 组件
│   │   ├── views/          # 页面
│   │   ├── api/            # API接口
│   │   └── utils/          # 工具类
│   └── package.json
└── database/               # 数据库脚本
    └── init.sql
```

## 开发计划
1. **第一阶段**: 搭建项目框架，创建数据库
2. **第二阶段**: 开发后端API接口
3. **第三阶段**: 开发前台展示页面
4. **第四阶段**: 开发后台管理系统
5. **第五阶段**: 美化界面，添加炫酷效果
6. **第六阶段**: 测试和优化

## 预期效果
- 一个视觉冲击力强的个人展示网站
- 简单易用的后台管理系统
- 多平台联系方式的集中展示
- 响应式设计，适配各种设备
