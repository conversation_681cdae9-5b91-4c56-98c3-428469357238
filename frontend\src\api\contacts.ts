import api from './index'

export interface ContactInfo {
  id?: number
  platform: string
  contactValue: string
  iconUrl?: string
  displayOrder: number
  isEnabled: boolean
  description?: string
  createTime?: string
  updateTime?: string
}

export interface ContactInfoDTO {
  platform: string
  contactValue: string
  iconUrl?: string
  displayOrder: number
  isEnabled?: boolean
  description?: string
}

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

/**
 * 获取公开的联系方式列表（前台展示）
 */
export const getPublicContacts = (): Promise<ApiResponse<ContactInfo[]>> => {
  return api.get('/contacts/public')
}

/**
 * 获取所有联系方式列表（后台管理）
 */
export const getAllContacts = (): Promise<ApiResponse<ContactInfo[]>> => {
  return api.get('/contacts/admin')
}

/**
 * 根据ID获取联系方式
 */
export const getContactById = (id: number): Promise<ApiResponse<ContactInfo>> => {
  return api.get(`/contacts/${id}`)
}

/**
 * 创建联系方式
 */
export const createContact = (data: ContactInfoDTO): Promise<ApiResponse<ContactInfo>> => {
  return api.post('/contacts', data)
}

/**
 * 更新联系方式
 */
export const updateContact = (id: number, data: ContactInfoDTO): Promise<ApiResponse<ContactInfo>> => {
  return api.put(`/contacts/${id}`, data)
}

/**
 * 删除联系方式
 */
export const deleteContact = (id: number): Promise<ApiResponse<string>> => {
  return api.delete(`/contacts/${id}`)
}

/**
 * 切换联系方式启用状态
 */
export const toggleContactStatus = (id: number): Promise<ApiResponse<ContactInfo>> => {
  return api.patch(`/contacts/${id}/toggle`)
}
