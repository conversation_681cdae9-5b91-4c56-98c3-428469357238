# FindMe - 个人防失联网站

一个炫酷的个人博客/防失联网站，主要用于展示个人联系方式，确保用户能够通过多种渠道联系到您。

## 🌟 项目特色

- **炫酷视觉效果**: 使用粒子背景、霓虹灯文字效果、动画过渡等
- **响应式设计**: 完美适配桌面端和移动端
- **现代化技术栈**: Java Spring Boot + Vue 3 + TypeScript
- **简洁管理后台**: 轻松管理联系方式信息
- **安全认证**: JWT token认证，保护管理接口

## 🚀 技术栈

### 后端
- **Java 17**
- **Spring Boot 3.2.0**
- **Spring Security** - 安全认证
- **Spring Data JPA** - 数据访问
- **MySQL 8.0** - 数据库
- **JWT** - 身份认证
- **Maven** - 项目管理

### 前端
- **Vue 3** - 前端框架
- **TypeScript** - 类型安全
- **Vite** - 构建工具
- **Element Plus** - UI组件库
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Axios** - HTTP客户端
- **Particles.js** - 粒子背景
- **Animate.css** - 动画效果
- **Typed.js** - 打字机效果

## 📁 项目结构

```
FindMe/
├── backend/                 # 后端项目
│   ├── src/main/java/
│   │   └── com/findme/
│   │       ├── controller/  # 控制器
│   │       ├── service/     # 服务层
│   │       ├── entity/      # 实体类
│   │       ├── repository/  # 数据访问层
│   │       ├── dto/         # 数据传输对象
│   │       ├── config/      # 配置类
│   │       ├── filter/      # 过滤器
│   │       └── util/        # 工具类
│   └── pom.xml
├── frontend/                # 前端项目
│   ├── src/
│   │   ├── components/      # 组件
│   │   ├── views/          # 页面
│   │   ├── api/            # API接口
│   │   ├── stores/         # 状态管理
│   │   ├── router/         # 路由配置
│   │   ├── styles/         # 样式文件
│   │   └── utils/          # 工具类
│   └── package.json
├── database/               # 数据库脚本
│   └── init.sql
├── 需求文档.md
└── README.md
```

## 🛠️ 快速开始

### 环境要求

- **Java**: 17+
- **Node.js**: 16+
- **MySQL**: 8.0+
- **Maven**: 3.6+

### 1. 数据库准备

```sql
-- 创建数据库
CREATE DATABASE findme CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入初始化脚本
mysql -u root -p findme < database/init.sql
```

### 2. 后端启动

```bash
cd backend

# 修改数据库配置
# 编辑 src/main/resources/application.yml
# 修改数据库连接信息

# 启动后端服务
mvn spring-boot:run
```

后端服务将在 `http://localhost:8080` 启动

### 3. 前端启动

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端服务将在 `http://localhost:3000` 启动

## 🔑 默认账户

- **管理员账户**: `admin`
- **默认密码**: `admin123`

## 📱 功能介绍

### 前台展示
- 炫酷的主页设计，展示"专注网易一手"标题
- 动态粒子背景效果
- 打字机效果的副标题
- 联系方式卡片展示（钉钉、飞书、微信、QQ）
- 点击复制联系方式功能
- 完全响应式设计

### 后台管理
- 安全的登录认证系统
- 仪表盘概览统计
- 联系方式的增删改查
- 联系方式启用/禁用切换
- 显示顺序调整
- 实时数据更新

## 🎨 界面预览

### 主页
- 渐变背景 + 粒子效果
- 霓虹灯文字效果
- 悬停动画卡片
- 炫酷按钮样式

### 管理后台
- 现代化管理界面
- 数据统计卡片
- 表格数据管理
- 响应式侧边栏

## 🔧 配置说明

### 后端配置 (application.yml)
```yaml
# 数据库配置
spring:
  datasource:
    url: **********************************
    username: your_username
    password: your_password

# JWT配置
jwt:
  secret: your-secret-key
  expiration: 86400000
```

### 前端配置 (vite.config.ts)
```typescript
// API代理配置
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true
    }
  }
}
```

## 📦 部署说明

### 后端部署
```bash
# 打包
mvn clean package

# 运行
java -jar target/findme-backend-1.0.0.jar
```

### 前端部署
```bash
# 构建
npm run build

# 部署 dist 目录到 Web 服务器
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址: [GitHub Repository]
- 作者: FindMe
- 邮箱: <EMAIL>

## 🙏 致谢

感谢以下开源项目的支持：
- Spring Boot
- Vue.js
- Element Plus
- Particles.js
- Animate.css
- 以及所有其他依赖项的作者们
