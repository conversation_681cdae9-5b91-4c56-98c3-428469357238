server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: findme-backend
  
  # 数据库配置
  datasource:
    url: *******************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
    
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    
# JWT配置
jwt:
  secret: findme-secret-key-2024-very-long-secret-key-for-security
  expiration: 86400000 # 24小时

# 日志配置
logging:
  level:
    com.findme: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
