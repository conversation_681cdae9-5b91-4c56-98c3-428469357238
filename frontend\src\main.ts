import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 引入Element Plus样式
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

// 引入动画库
import 'animate.css'

// 引入全局样式
import './styles/global.scss'

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.mount('#app')

console.log('FindMe Frontend Started Successfully!')
console.log('Version: 1.0.0')
console.log('Author: FindMe')
