import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as apiLogin, validateToken, type LoginRequest } from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>('')
  const username = ref<string>('')
  const email = ref<string>('')
  const loading = ref<boolean>(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)

  // 登录
  const login = async (loginData: LoginRequest) => {
    try {
      loading.value = true
      const response = await apiLogin(loginData)
      
      if (response.data) {
        token.value = response.data.token
        username.value = response.data.username
        email.value = response.data.email
        
        // 保存到本地存储
        localStorage.setItem('token', token.value)
        localStorage.setItem('username', username.value)
        localStorage.setItem('email', email.value)
        
        ElMessage.success('登录成功')
        return true
      }
      return false
    } catch (error) {
      console.error('登录失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    token.value = ''
    username.value = ''
    email.value = ''
    
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('username')
    localStorage.removeItem('email')
    
    ElMessage.success('已退出登录')
  }

  // 检查认证状态
  const checkAuth = async () => {
    const savedToken = localStorage.getItem('token')
    const savedUsername = localStorage.getItem('username')
    const savedEmail = localStorage.getItem('email')
    
    if (savedToken && savedUsername) {
      try {
        // 验证token是否有效
        await validateToken(savedToken)
        
        token.value = savedToken
        username.value = savedUsername
        email.value = savedEmail || ''
        
        return true
      } catch (error) {
        console.error('Token验证失败:', error)
        logout()
        return false
      }
    }
    
    return false
  }

  // 初始化时检查认证状态
  checkAuth()

  return {
    // 状态
    token,
    username,
    email,
    loading,
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    login,
    logout,
    checkAuth
  }
})
