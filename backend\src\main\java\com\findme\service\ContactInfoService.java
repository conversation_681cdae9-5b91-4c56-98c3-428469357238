package com.findme.service;

import com.findme.dto.ContactInfoDTO;
import com.findme.entity.ContactInfo;
import com.findme.repository.ContactInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 联系方式信息服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContactInfoService {

    private final ContactInfoRepository contactInfoRepository;

    /**
     * 获取所有启用的联系方式（前台展示）
     * 
     * @return 联系方式列表
     */
    public List<ContactInfo> getEnabledContacts() {
        return contactInfoRepository.findByIsEnabledTrueOrderByDisplayOrderAsc();
    }

    /**
     * 获取所有联系方式（后台管理）
     * 
     * @return 联系方式列表
     */
    public List<ContactInfo> getAllContacts() {
        return contactInfoRepository.findAllByOrderByDisplayOrderAsc();
    }

    /**
     * 根据ID获取联系方式
     * 
     * @param id 联系方式ID
     * @return 联系方式信息
     */
    public ContactInfo getContactById(Long id) {
        Optional<ContactInfo> contactOptional = contactInfoRepository.findById(id);
        if (contactOptional.isEmpty()) {
            throw new RuntimeException("联系方式不存在");
        }
        return contactOptional.get();
    }

    /**
     * 创建联系方式
     * 
     * @param contactInfoDTO 联系方式DTO
     * @return 创建的联系方式
     */
    @Transactional
    public ContactInfo createContact(ContactInfoDTO contactInfoDTO) {
        log.info("创建联系方式: {}", contactInfoDTO.getPlatform());

        ContactInfo contactInfo = new ContactInfo();
        BeanUtils.copyProperties(contactInfoDTO, contactInfo);

        // 如果没有指定显示顺序，设置为最大值+1
        if (contactInfo.getDisplayOrder() == null || contactInfo.getDisplayOrder() == 0) {
            Integer maxOrder = contactInfoRepository.findMaxDisplayOrder();
            contactInfo.setDisplayOrder(maxOrder + 1);
        }

        ContactInfo savedContact = contactInfoRepository.save(contactInfo);
        log.info("联系方式创建成功: {}", savedContact.getId());
        return savedContact;
    }

    /**
     * 更新联系方式
     * 
     * @param id 联系方式ID
     * @param contactInfoDTO 联系方式DTO
     * @return 更新的联系方式
     */
    @Transactional
    public ContactInfo updateContact(Long id, ContactInfoDTO contactInfoDTO) {
        log.info("更新联系方式: {}", id);

        ContactInfo existingContact = getContactById(id);
        BeanUtils.copyProperties(contactInfoDTO, existingContact, "id", "createTime");

        ContactInfo updatedContact = contactInfoRepository.save(existingContact);
        log.info("联系方式更新成功: {}", updatedContact.getId());
        return updatedContact;
    }

    /**
     * 删除联系方式
     * 
     * @param id 联系方式ID
     */
    @Transactional
    public void deleteContact(Long id) {
        log.info("删除联系方式: {}", id);

        if (!contactInfoRepository.existsById(id)) {
            throw new RuntimeException("联系方式不存在");
        }

        contactInfoRepository.deleteById(id);
        log.info("联系方式删除成功: {}", id);
    }

    /**
     * 切换联系方式启用状态
     * 
     * @param id 联系方式ID
     * @return 更新后的联系方式
     */
    @Transactional
    public ContactInfo toggleContactStatus(Long id) {
        log.info("切换联系方式状态: {}", id);

        ContactInfo contactInfo = getContactById(id);
        contactInfo.setIsEnabled(!contactInfo.getIsEnabled());

        ContactInfo updatedContact = contactInfoRepository.save(contactInfo);
        log.info("联系方式状态切换成功: {} -> {}", id, updatedContact.getIsEnabled());
        return updatedContact;
    }

    /**
     * 初始化默认联系方式数据
     */
    @Transactional
    public void initDefaultContacts() {
        if (contactInfoRepository.count() == 0) {
            log.info("初始化默认联系方式数据");

            ContactInfo[] defaultContacts = {
                new ContactInfo("钉钉", "请设置您的钉钉号", "https://img.alicdn.com/imgextra/i1/O1CN01TleQVX1OjB8BVn7kR_!!6000000001737-2-tps-1024-1024.png", 1),
                new ContactInfo("飞书", "请设置您的飞书号", "https://sf1-cdn-tos.huoshanstatic.com/obj/eden-cn/ptlz_zlp/ljhwZthlaukjlkulzlp/root-web-sites/webicon.png", 2),
                new ContactInfo("微信", "请设置您的微信号", "https://res.wx.qq.com/a/wx_fed/assets/res/OTE0YTAw.png", 3),
                new ContactInfo("QQ", "请设置您的QQ号", "https://qzonestyle.gtimg.cn/qzone/qzact/act/external/qq-logo.png", 4)
            };

            for (ContactInfo contact : defaultContacts) {
                contactInfoRepository.save(contact);
            }

            log.info("默认联系方式数据初始化完成");
        }
    }
}
