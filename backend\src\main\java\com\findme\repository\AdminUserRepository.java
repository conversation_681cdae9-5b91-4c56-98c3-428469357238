package com.findme.repository;

import com.findme.entity.AdminUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 管理员用户数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface AdminUserRepository extends JpaRepository<AdminUser, Long> {

    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    Optional<AdminUser> findByUsername(String username);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 根据用户名和启用状态查找用户
     * 
     * @param username 用户名
     * @param isEnabled 是否启用
     * @return 用户信息
     */
    Optional<AdminUser> findByUsernameAndIsEnabled(String username, Boolean isEnabled);
}
