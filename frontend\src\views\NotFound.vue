<template>
  <div class="not-found-container">
    <!-- 粒子背景 -->
    <div id="notfound-particles" class="particles-container"></div>
    
    <!-- 404内容 -->
    <div class="not-found-content">
      <div class="container">
        <div class="error-section animate__animated animate__fadeInDown">
          <h1 class="error-code neon-text">404</h1>
          <h2 class="error-title">页面未找到</h2>
          <p class="error-description">
            抱歉，您访问的页面不存在或已被移除。
          </p>
        </div>
        
        <div class="actions-section animate__animated animate__fadeInUp animate__delay-1s">
          <el-button type="primary" size="large" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          
          <el-button size="large" @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
        </div>
        
        <!-- 装饰性元素 -->
        <div class="decoration animate__animated animate__pulse animate__infinite">
          <div class="floating-icon">
            <el-icon><QuestionFilled /></el-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { House, ArrowLeft, QuestionFilled } from '@element-plus/icons-vue'

const router = useRouter()

// 初始化粒子背景
const initParticles = () => {
  import('particles.js').then((particlesJS) => {
    particlesJS.particlesJS('notfound-particles', {
      particles: {
        number: {
          value: 60,
          density: {
            enable: true,
            value_area: 800
          }
        },
        color: {
          value: '#ffffff'
        },
        shape: {
          type: 'circle'
        },
        opacity: {
          value: 0.3,
          random: true
        },
        size: {
          value: 3,
          random: true
        },
        line_linked: {
          enable: true,
          distance: 150,
          color: '#ffffff',
          opacity: 0.2,
          width: 1
        },
        move: {
          enable: true,
          speed: 3,
          direction: 'none',
          random: true,
          straight: false,
          out_mode: 'out',
          bounce: false
        }
      },
      interactivity: {
        detect_on: 'canvas',
        events: {
          onhover: {
            enable: true,
            mode: 'bubble'
          },
          onclick: {
            enable: true,
            mode: 'push'
          },
          resize: true
        },
        modes: {
          bubble: {
            distance: 200,
            size: 6,
            duration: 2,
            opacity: 0.8,
            speed: 3
          },
          push: {
            particles_nb: 4
          }
        }
      },
      retina_detect: true
    })
  })
}

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

// 组件挂载
onMounted(() => {
  setTimeout(initParticles, 300)
})
</script>

<style lang="scss" scoped>
.not-found-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.not-found-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  text-align: center;
}

.error-section {
  margin-bottom: 40px;
}

.error-code {
  font-size: 8rem;
  font-weight: 900;
  margin-bottom: 20px;
  letter-spacing: 5px;
  line-height: 1;
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 15px;
  letter-spacing: 2px;
}

.error-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0;
  line-height: 1.6;
}

.actions-section {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 60px;
  
  .el-button {
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    
    &--primary {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      border: none;
      
      &:hover {
        background: linear-gradient(135deg, #00f2fe 0%, #4facfe 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 242, 254, 0.3);
      }
    }
    
    &:not(.el-button--primary) {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: #fff;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
      }
    }
  }
}

.decoration {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  opacity: 0.1;
}

.floating-icon {
  font-size: 20rem;
  color: #fff;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-code {
    font-size: 5rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-description {
    font-size: 1rem;
    padding: 0 20px;
  }
  
  .actions-section {
    flex-direction: column;
    align-items: center;
    gap: 15px;
    
    .el-button {
      width: 200px;
    }
  }
  
  .floating-icon {
    font-size: 10rem;
  }
}

@media (max-width: 480px) {
  .error-code {
    font-size: 4rem;
  }
  
  .error-title {
    font-size: 1.5rem;
  }
  
  .floating-icon {
    font-size: 8rem;
  }
}
</style>
