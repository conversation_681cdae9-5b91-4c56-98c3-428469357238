<template>
  <div class="contacts-management">
    <div class="page-header">
      <h1>联系方式管理</h1>
      <el-button type="primary" @click="showAddDialog">
        <el-icon><Plus /></el-icon>
        添加联系方式
      </el-button>
    </div>
    
    <!-- 联系方式列表 -->
    <el-card>
      <el-table
        :data="contactsStore.adminContacts"
        v-loading="contactsStore.loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="图标" width="80">
          <template #default="{ row }">
            <img 
              :src="row.iconUrl" 
              :alt="row.platform"
              class="contact-icon"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="platform" label="平台" width="120" />
        
        <el-table-column prop="contactValue" label="联系方式" min-width="200" />
        
        <el-table-column prop="displayOrder" label="排序" width="80" />
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isEnabled ? 'success' : 'danger'">
              {{ row.isEnabled ? '已启用' : '已禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.updateTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(row)"
            >
              编辑
            </el-button>
            
            <el-button
              :type="row.isEnabled ? 'warning' : 'success'"
              size="small"
              @click="toggleStatus(row)"
            >
              {{ row.isEnabled ? '禁用' : '启用' }}
            </el-button>
            
            <el-button
              type="danger"
              size="small"
              @click="deleteContact(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="平台名称" prop="platform">
          <el-input
            v-model="formData.platform"
            placeholder="请输入平台名称"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="联系方式" prop="contactValue">
          <el-input
            v-model="formData.contactValue"
            placeholder="请输入联系方式"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="图标URL" prop="iconUrl">
          <el-input
            v-model="formData.iconUrl"
            placeholder="请输入图标URL"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="显示顺序" prop="displayOrder">
          <el-input-number
            v-model="formData.displayOrder"
            :min="1"
            :max="100"
            controls-position="right"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch
            v-model="formData.isEnabled"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="contactsStore.loading"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useContactsStore } from '@/stores/contacts'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { ContactInfo, ContactInfoDTO } from '@/api/contacts'

const contactsStore = useContactsStore()

// 响应式数据
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentEditId = ref<number | null>(null)
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<ContactInfoDTO>({
  platform: '',
  contactValue: '',
  iconUrl: '',
  displayOrder: 1,
  isEnabled: true,
  description: ''
})

// 表单验证规则
const formRules: FormRules = {
  platform: [
    { required: true, message: '请输入平台名称', trigger: 'blur' },
    { min: 2, max: 20, message: '平台名称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  contactValue: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { min: 1, max: 100, message: '联系方式长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  displayOrder: [
    { required: true, message: '请输入显示顺序', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑联系方式' : '添加联系方式')

// 格式化日期时间
const formatDateTime = (dateStr?: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString()
}

// 显示添加对话框
const showAddDialog = () => {
  isEdit.value = false
  currentEditId.value = null
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (contact: ContactInfo) => {
  isEdit.value = true
  currentEditId.value = contact.id!
  
  // 填充表单数据
  Object.assign(formData, {
    platform: contact.platform,
    contactValue: contact.contactValue,
    iconUrl: contact.iconUrl,
    displayOrder: contact.displayOrder,
    isEnabled: contact.isEnabled,
    description: contact.description
  })
  
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  Object.assign(formData, {
    platform: '',
    contactValue: '',
    iconUrl: '',
    displayOrder: 1,
    isEnabled: true,
    description: ''
  })
}

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    let success = false
    
    if (isEdit.value && currentEditId.value) {
      success = await contactsStore.editContact(currentEditId.value, formData)
    } else {
      success = await contactsStore.addContact(formData)
    }
    
    if (success) {
      dialogVisible.value = false
      resetForm()
    }
  } catch (error) {
    console.error('提交失败:', error)
  }
}

// 切换状态
const toggleStatus = async (contact: ContactInfo) => {
  if (!contact.id) return
  
  try {
    await ElMessageBox.confirm(
      `确定要${contact.isEnabled ? '禁用' : '启用'}该联系方式吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await contactsStore.toggleStatus(contact.id)
  } catch (error) {
    // 用户取消
  }
}

// 删除联系方式
const deleteContact = async (contact: ContactInfo) => {
  if (!contact.id) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除联系方式"${contact.platform}"吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    await contactsStore.removeContact(contact.id)
  } catch (error) {
    // 用户取消
  }
}

// 组件挂载
onMounted(async () => {
  await contactsStore.fetchAdminContacts()
})
</script>

<style lang="scss" scoped>
.contacts-management {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    font-size: 1.8rem;
    color: #333;
    margin: 0;
  }
}

.contact-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;

    h1 {
      font-size: 1.5rem;
    }
  }

  :deep(.el-table) {
    .el-table__body-wrapper {
      overflow-x: auto;
    }
  }
}
</style>
