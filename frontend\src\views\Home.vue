<template>
  <div class="home-container">
    <!-- 粒子背景 -->
    <div id="particles-js" class="particles-container"></div>
    
    <!-- 主要内容 -->
    <div class="main-content">
      <div class="container">
        <!-- 主标题 -->
        <div class="title-section animate__animated animate__fadeInDown">
          <h1 class="main-title neon-text">专注网易一手</h1>
          <div class="subtitle-container">
            <p class="subtitle" ref="subtitleRef">找到我见下</p>
          </div>
        </div>
        
        <!-- 联系方式卡片 -->
        <div class="contacts-section animate__animated animate__fadeInUp animate__delay-1s">
          <div class="contacts-grid">
            <div 
              v-for="(contact, index) in contacts" 
              :key="contact.id"
              class="contact-card hover-card animate__animated animate__zoomIn"
              :style="{ animationDelay: `${1.5 + index * 0.2}s` }"
              @click="copyToClipboard(contact.contactValue)"
            >
              <div class="card-inner">
                <div class="icon-container">
                  <img :src="contact.iconUrl" :alt="contact.platform" class="contact-icon" />
                </div>
                <h3 class="platform-name">{{ contact.platform }}</h3>
                <p class="contact-value">{{ contact.contactValue }}</p>
                <div class="copy-hint">点击复制</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 管理员入口 -->
        <div class="admin-entrance animate__animated animate__fadeIn animate__delay-2s">
          <button class="cyber-button" @click="goToAdmin">
            管理后台
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useContactsStore } from '@/stores/contacts'
import { ElMessage } from 'element-plus'
import Typed from 'typed.js'

const router = useRouter()
const contactsStore = useContactsStore()

// 响应式数据
const contacts = ref(contactsStore.publicContacts)
const subtitleRef = ref<HTMLElement>()
let typedInstance: Typed | null = null

// 初始化粒子背景
const initParticles = () => {
  // 动态导入particles.js
  import('particles.js').then((particlesJS) => {
    particlesJS.particlesJS('particles-js', {
      particles: {
        number: {
          value: 80,
          density: {
            enable: true,
            value_area: 800
          }
        },
        color: {
          value: '#ffffff'
        },
        shape: {
          type: 'circle',
          stroke: {
            width: 0,
            color: '#000000'
          }
        },
        opacity: {
          value: 0.5,
          random: false,
          anim: {
            enable: false,
            speed: 1,
            opacity_min: 0.1,
            sync: false
          }
        },
        size: {
          value: 3,
          random: true,
          anim: {
            enable: false,
            speed: 40,
            size_min: 0.1,
            sync: false
          }
        },
        line_linked: {
          enable: true,
          distance: 150,
          color: '#ffffff',
          opacity: 0.4,
          width: 1
        },
        move: {
          enable: true,
          speed: 6,
          direction: 'none',
          random: false,
          straight: false,
          out_mode: 'out',
          bounce: false,
          attract: {
            enable: false,
            rotateX: 600,
            rotateY: 1200
          }
        }
      },
      interactivity: {
        detect_on: 'canvas',
        events: {
          onhover: {
            enable: true,
            mode: 'repulse'
          },
          onclick: {
            enable: true,
            mode: 'push'
          },
          resize: true
        },
        modes: {
          grab: {
            distance: 400,
            line_linked: {
              opacity: 1
            }
          },
          bubble: {
            distance: 400,
            size: 40,
            duration: 2,
            opacity: 8,
            speed: 3
          },
          repulse: {
            distance: 200,
            duration: 0.4
          },
          push: {
            particles_nb: 4
          },
          remove: {
            particles_nb: 2
          }
        }
      },
      retina_detect: true
    })
  })
}

// 初始化打字机效果
const initTyped = () => {
  if (subtitleRef.value) {
    typedInstance = new Typed(subtitleRef.value, {
      strings: ['找到我见下', 'Find Me Below', '联系我吧'],
      typeSpeed: 100,
      backSpeed: 50,
      backDelay: 2000,
      loop: true,
      showCursor: true,
      cursorChar: '|'
    })
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 跳转到管理后台
const goToAdmin = () => {
  router.push('/admin')
}

// 组件挂载
onMounted(async () => {
  // 获取联系方式数据
  await contactsStore.fetchPublicContacts()
  
  // 初始化粒子背景
  setTimeout(initParticles, 500)
  
  // 初始化打字机效果
  setTimeout(initTyped, 1000)
})

// 组件卸载
onUnmounted(() => {
  if (typedInstance) {
    typedInstance.destroy()
  }
})
</script>

<style lang="scss" scoped>
.home-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.main-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px 0;
}

.title-section {
  text-align: center;
  margin-bottom: 60px;
}

.main-title {
  font-size: 4rem;
  font-weight: 900;
  margin-bottom: 20px;
  letter-spacing: 3px;
}

.subtitle-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.subtitle {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
  letter-spacing: 2px;
}

.contacts-section {
  margin-bottom: 60px;
}

.contacts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

.contact-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }
}

.card-inner {
  position: relative;
}

.icon-container {
  margin-bottom: 20px;
}

.contact-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.platform-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 10px;
  letter-spacing: 1px;
}

.contact-value {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 15px;
  word-break: break-all;
}

.copy-hint {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.contact-card:hover .copy-hint {
  opacity: 1;
}

.admin-entrance {
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1.2rem;
  }
  
  .contacts-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .contact-card {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
}
</style>
