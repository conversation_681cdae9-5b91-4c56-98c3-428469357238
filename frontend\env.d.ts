/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'particles.js' {
  export function particlesJS(tagId: string, options: any): void
}

declare module 'typed.js' {
  export default class Typed {
    constructor(element: string | Element, options: any)
    destroy(): void
  }
}
