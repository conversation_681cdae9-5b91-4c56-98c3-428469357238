-- FindMe数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS findme CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE findme;

-- 管理员用户表
CREATE TABLE IF NOT EXISTS admin_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    email VARCHAR(100) COMMENT '邮箱',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_is_enabled (is_enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表';

-- 联系方式信息表
CREATE TABLE IF NOT EXISTS contact_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    platform VARCHAR(50) NOT NULL COMMENT '平台名称',
    contact_value VARCHAR(200) NOT NULL COMMENT '联系方式值',
    icon_url VARCHAR(500) COMMENT '图标URL',
    display_order INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    description VARCHAR(500) COMMENT '描述',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_platform (platform),
    INDEX idx_display_order (display_order),
    INDEX idx_is_enabled (is_enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系方式信息表';

-- 插入默认管理员用户 (密码: admin123)
INSERT INTO admin_user (username, password, email, is_enabled) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '<EMAIL>', TRUE)
ON DUPLICATE KEY UPDATE username = username;

-- 插入默认联系方式数据
INSERT INTO contact_info (platform, contact_value, icon_url, display_order, is_enabled, description) VALUES 
('钉钉', '请设置您的钉钉号', 'https://img.alicdn.com/imgextra/i1/O1CN01TleQVX1OjB8BVn7kR_!!6000000001737-2-tps-1024-1024.png', 1, TRUE, '钉钉联系方式'),
('飞书', '请设置您的飞书号', 'https://sf1-cdn-tos.huoshanstatic.com/obj/eden-cn/ptlz_zlp/ljhwZthlaukjlkulzlp/root-web-sites/webicon.png', 2, TRUE, '飞书联系方式'),
('微信', '请设置您的微信号', 'https://res.wx.qq.com/a/wx_fed/assets/res/OTE0YTAw.png', 3, TRUE, '微信联系方式'),
('QQ', '请设置您的QQ号', 'https://qzonestyle.gtimg.cn/qzone/qzact/act/external/qq-logo.png', 4, TRUE, 'QQ联系方式')
ON DUPLICATE KEY UPDATE platform = platform;
