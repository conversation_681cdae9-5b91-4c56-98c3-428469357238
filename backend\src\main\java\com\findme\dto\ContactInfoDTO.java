package com.findme.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 联系方式信息DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class ContactInfoDTO {

    private Long id;

    @NotBlank(message = "平台名称不能为空")
    private String platform;

    @NotBlank(message = "联系方式不能为空")
    private String contactValue;

    private String iconUrl;

    @NotNull(message = "显示顺序不能为空")
    private Integer displayOrder;

    private Boolean isEnabled = true;

    private String description;
}
