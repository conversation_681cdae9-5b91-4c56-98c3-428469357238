<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

onMounted(() => {
  // 移除加载动画
  const loading = document.querySelector('.loading')
  if (loading) {
    setTimeout(() => {
      loading.style.opacity = '0'
      setTimeout(() => {
        loading.remove()
      }, 500)
    }, 1000)
  }
})
</script>

<style lang="scss">
#app {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}
</style>
