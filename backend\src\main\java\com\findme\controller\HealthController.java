package com.findme.controller;

import com.findme.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/health")
@Slf4j
@CrossOrigin(origins = "*")
public class HealthController {

    /**
     * 健康检查
     * 
     * @return 健康状态
     */
    @GetMapping
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("application", "FindMe Backend");
        healthInfo.put("version", "1.0.0");
        
        return ApiResponse.success("服务正常", healthInfo);
    }
}
