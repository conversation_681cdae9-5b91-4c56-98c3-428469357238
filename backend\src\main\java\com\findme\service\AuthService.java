package com.findme.service;

import com.findme.dto.LoginRequest;
import com.findme.entity.AdminUser;
import com.findme.repository.AdminUserRepository;
import com.findme.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 认证服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthService {

    private final AdminUserRepository adminUserRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;

    /**
     * 用户登录
     * 
     * @param loginRequest 登录请求
     * @return 登录结果
     */
    public Map<String, Object> login(LoginRequest loginRequest) {
        log.info("用户登录尝试: {}", loginRequest.getUsername());

        Optional<AdminUser> userOptional = adminUserRepository
                .findByUsernameAndIsEnabled(loginRequest.getUsername(), true);

        if (userOptional.isEmpty()) {
            log.warn("用户不存在或已禁用: {}", loginRequest.getUsername());
            throw new RuntimeException("用户名或密码错误");
        }

        AdminUser user = userOptional.get();
        if (!passwordEncoder.matches(loginRequest.getPassword(), user.getPassword())) {
            log.warn("密码错误: {}", loginRequest.getUsername());
            throw new RuntimeException("用户名或密码错误");
        }

        // 生成JWT Token
        String token = jwtUtil.generateToken(user.getUsername());

        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("username", user.getUsername());
        result.put("email", user.getEmail());

        log.info("用户登录成功: {}", loginRequest.getUsername());
        return result;
    }

    /**
     * 验证Token
     * 
     * @param token JWT Token
     * @return 用户信息
     */
    public AdminUser validateToken(String token) {
        if (!jwtUtil.validateToken(token)) {
            throw new RuntimeException("Token无效");
        }

        String username = jwtUtil.getUsernameFromToken(token);
        Optional<AdminUser> userOptional = adminUserRepository
                .findByUsernameAndIsEnabled(username, true);

        if (userOptional.isEmpty()) {
            throw new RuntimeException("用户不存在或已禁用");
        }

        return userOptional.get();
    }

    /**
     * 创建默认管理员用户
     */
    public void createDefaultAdmin() {
        if (!adminUserRepository.existsByUsername("admin")) {
            AdminUser admin = new AdminUser();
            admin.setUsername("admin");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setEmail("<EMAIL>");
            admin.setIsEnabled(true);
            
            adminUserRepository.save(admin);
            log.info("默认管理员用户创建成功: admin/admin123");
        }
    }
}
